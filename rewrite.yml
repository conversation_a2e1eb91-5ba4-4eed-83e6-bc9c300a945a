---
type: specs.openrewrite.org/v1beta/recipe
name: com.oocl.migration.LoggerMigration
displayName: Migrate from SLF4J Logger to OLogger
description: Migrates usage of org.slf4j.Logger to com.oocl.OLogger singleton
recipeList:
  # Replace Logger type imports
  - org.openrewrite.java.ChangeType:
      oldFullyQualifiedTypeName: org.slf4j.Logger
      newFullyQualifiedTypeName: com.oocl.OLogger

  # Remove LoggerFactory.getLogger() calls and replace with OLogger singleton
  - org.openrewrite.java.search.FindMethods:
      methodPattern: org.slf4j.LoggerFactory getLogger(..)
      matchOverrides: true
  - org.openrewrite.java.ChangeMethodTargetToStatic:
      methodPattern: org.slf4j.LoggerFactory getLogger(..)
      fullyQualifiedTargetTypeName: com.oocl.OLogger

  # Clean up any remaining Logger field declarations
  - org.openrewrite.java.RemoveUnusedImports

---
type: specs.openrewrite.org/v1beta/style
name: com.oocl.migration.LoggerStyle
styleConfigs:
  - org.openrewrite.java.style.ImportLayout:
      removeUnused: true
